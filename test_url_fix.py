#!/usr/bin/env python3
"""
测试URL修复的脚本
"""
import requests

# API基础URL
BASE_URL = "http://127.0.0.1:8000"

def login():
    """登录并获取token"""
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/token", data=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    else:
        print(f"登录失败: {response.text}")
        return None

def test_url_patterns(headers):
    """测试不同的URL模式"""
    print("=== 测试URL模式 ===")
    
    # 测试带尾部斜杠的URL
    print("\n1. 测试带尾部斜杠的URL:")
    response = requests.get(f"{BASE_URL}/transactions/", headers=headers)
    print(f"GET /transactions/ - 状态: {response.status_code}")
    
    # 测试不带尾部斜杠的URL
    print("\n2. 测试不带尾部斜杠的URL:")
    response = requests.get(f"{BASE_URL}/transactions", headers=headers)
    print(f"GET /transactions - 状态: {response.status_code}")
    
    # 测试PUT请求 - 带尾部斜杠
    if response.status_code == 200:
        transactions = response.json()
        if transactions:
            transaction_id = transactions[0]["id"]
            
            print(f"\n3. 测试PUT请求 - 带尾部斜杠:")
            update_data = {"title": "测试更新"}
            response = requests.put(f"{BASE_URL}/transactions/{transaction_id}/", json=update_data, headers=headers)
            print(f"PUT /transactions/{transaction_id}/ - 状态: {response.status_code}")
            if response.status_code != 200:
                print(f"响应: {response.text}")
            
            print(f"\n4. 测试PUT请求 - 不带尾部斜杠:")
            response = requests.put(f"{BASE_URL}/transactions/{transaction_id}", json=update_data, headers=headers)
            print(f"PUT /transactions/{transaction_id} - 状态: {response.status_code}")
            if response.status_code != 200:
                print(f"响应: {response.text}")

def test_frontend_proxy():
    """测试前端代理"""
    print("\n=== 测试前端代理 ===")
    
    # 测试前端代理的健康检查
    try:
        response = requests.get("http://localhost:3000/api/health", timeout=5)
        print(f"前端代理健康检查: {response.status_code}")
        if response.status_code == 200:
            print(f"响应: {response.json()}")
    except Exception as e:
        print(f"前端代理测试失败: {e}")

def main():
    """主函数"""
    print("开始测试URL修复...")
    
    # 测试前端代理
    test_frontend_proxy()
    
    # 登录
    headers = login()
    if not headers:
        print("登录失败，无法继续测试")
        return
    
    # 测试URL模式
    test_url_patterns(headers)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
