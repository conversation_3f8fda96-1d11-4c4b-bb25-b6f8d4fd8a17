#!/usr/bin/env python3
"""
测试前端CRUD问题的脚本
"""
import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://127.0.0.1:8000"

def login():
    """登录并获取token"""
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/token", data=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    else:
        print(f"登录失败: {response.text}")
        return None

def test_transaction_edit_scenario(headers):
    """测试您描述的编辑场景"""
    print("=== 测试交易记录编辑场景 ===")
    
    # 1. 获取现有交易记录
    response = requests.get(f"{BASE_URL}/transactions/", headers=headers)
    if response.status_code != 200:
        print(f"获取交易记录失败: {response.text}")
        return False
    
    transactions = response.json()
    if not transactions:
        print("没有现有交易记录，先创建一个")
        
        # 获取账户
        accounts_response = requests.get(f"{BASE_URL}/accounts/", headers=headers)
        if accounts_response.status_code != 200:
            print("无法获取账户")
            return False
        
        accounts = accounts_response.json()
        if not accounts:
            print("没有可用账户")
            return False
        
        # 创建测试交易
        new_transaction = {
            "account_id": accounts[0]["id"],
            "project_id": None,
            "category_id": None,
            "type": "income",
            "title": "test",
            "amount": 100,
            "currency": "CNY",
            "transaction_date": "2025-06-08T17:33:00",
            "notes": ""
        }
        
        response = requests.post(f"{BASE_URL}/transactions/", json=new_transaction, headers=headers)
        if response.status_code != 200:
            print(f"创建测试交易失败: {response.text}")
            return False
        
        created_transaction = response.json()
        print(f"创建了测试交易: {created_transaction}")
        transaction_to_edit = created_transaction
    else:
        transaction_to_edit = transactions[0]
        print(f"使用现有交易进行测试: {transaction_to_edit}")
    
    # 2. 模拟编辑操作 - 不修改任何内容，直接保存
    print(f"\n正在编辑交易 ID: {transaction_to_edit['id']}")
    
    # 准备更新数据 - 模拟前端发送的数据
    update_data = {
        "title": transaction_to_edit["title"],
        "type": transaction_to_edit["type"],
        "amount": float(transaction_to_edit["amount"]),
        "account_id": transaction_to_edit["account_id"],
        "project_id": transaction_to_edit.get("project_id"),
        "transaction_date": transaction_to_edit["transaction_date"],
        "notes": transaction_to_edit.get("notes", ""),
        "currency": "CNY"
    }
    
    print(f"发送的更新数据: {json.dumps(update_data, indent=2, default=str)}")
    
    # 3. 发送PUT请求
    response = requests.put(
        f"{BASE_URL}/transactions/{transaction_to_edit['id']}/", 
        json=update_data, 
        headers=headers
    )
    
    print(f"更新响应状态: {response.status_code}")
    
    if response.status_code == 200:
        updated_transaction = response.json()
        print(f"更新成功: {updated_transaction}")
        return True
    else:
        print(f"更新失败: {response.text}")
        print(f"响应头: {dict(response.headers)}")
        
        # 检查是否是认证问题
        if response.status_code == 401:
            print("认证失败 - token可能已过期")
            
            # 尝试验证token
            verify_response = requests.get(f"{BASE_URL}/auth/verify/", headers=headers)
            print(f"Token验证状态: {verify_response.status_code}")
            if verify_response.status_code != 200:
                print(f"Token验证失败: {verify_response.text}")
        
        return False

def main():
    """主函数"""
    print("开始测试前端CRUD问题...")
    
    # 登录
    headers = login()
    if not headers:
        print("登录失败，无法继续测试")
        return
    
    # 测试编辑场景
    success = test_transaction_edit_scenario(headers)
    
    if success:
        print("\n✅ 测试通过 - 后端CRUD功能正常")
    else:
        print("\n❌ 测试失败 - 发现问题")

if __name__ == "__main__":
    main()
