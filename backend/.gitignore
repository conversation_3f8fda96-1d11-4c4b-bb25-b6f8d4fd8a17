# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environment
.env
.venv
env/
venv/
ENV/
env.bak
venv.bak

# Databases
*.sqlite3
*.db
*.sql
*.pgsql
dump.rdb

# Testing
.pytest_cache/
.tox/
htmlcov/
.coverage
.coverage.*
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# Logs and compiled files
*.log
*.pot
*.pyc

# Runtime data
celerybeat-schedule
celerybeat.pid

# Secrets
.secrets
secrets.toml
secrets.yaml
secrets.yml
*.ini
*.env.*
!*.env.example

# Docker
docker-compose.override.yml